{"id": "a97a2bf2-07a5-4cef-995d-1da9297aec63", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.quiz_questions": {"name": "quiz_questions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "quiz_id": {"name": "quiz_id", "type": "uuid", "primaryKey": false, "notNull": true}, "question_type": {"name": "question_type", "type": "question_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "question_text": {"name": "question_text", "type": "text", "primaryKey": false, "notNull": true}, "options": {"name": "options", "type": "text", "primaryKey": false, "notNull": false}, "correct_answer": {"name": "correct_answer", "type": "text", "primaryKey": false, "notNull": true}, "explanation": {"name": "explanation", "type": "text", "primaryKey": false, "notNull": false}, "points": {"name": "points", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"quiz_questions_quiz_id_quizzes_id_fk": {"name": "quiz_questions_quiz_id_quizzes_id_fk", "tableFrom": "quiz_questions", "tableTo": "quizzes", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quizzes": {"name": "quizzes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "topic_id": {"name": "topic_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "instructions": {"name": "instructions", "type": "text", "primaryKey": false, "notNull": false}, "time_limit": {"name": "time_limit", "type": "integer", "primaryKey": false, "notNull": false}, "passing_score": {"name": "passing_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "max_attempts": {"name": "max_attempts", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"quizzes_topic_id_topics_id_fk": {"name": "quizzes_topic_id_topics_id_fk", "tableFrom": "quizzes", "tableTo": "topics", "columnsFrom": ["topic_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subjects": {"name": "subjects", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "standard": {"name": "standard", "type": "standard", "typeSchema": "public", "primaryKey": false, "notNull": true}, "educational_board": {"name": "educational_board", "type": "educational_board", "typeSchema": "public", "primaryKey": false, "notNull": true}, "icon_url": {"name": "icon_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "color_code": {"name": "color_code", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.topics": {"name": "topics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "subject_id": {"name": "subject_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "difficulty_level": {"name": "difficulty_level", "type": "difficulty_level", "typeSchema": "public", "primaryKey": false, "notNull": true}, "estimated_duration": {"name": "estimated_duration", "type": "integer", "primaryKey": false, "notNull": false}, "prerequisites": {"name": "prerequisites", "type": "text", "primaryKey": false, "notNull": false}, "learning_objectives": {"name": "learning_objectives", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"topics_subject_id_subjects_id_fk": {"name": "topics_subject_id_subjects_id_fk", "tableFrom": "topics", "tableTo": "subjects", "columnsFrom": ["subject_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.videos": {"name": "videos", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "topic_id": {"name": "topic_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "video_url": {"name": "video_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "thumbnail_url": {"name": "thumbnail_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "transcript_url": {"name": "transcript_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "content_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'DRAFT'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"videos_topic_id_topics_id_fk": {"name": "videos_topic_id_topics_id_fk", "tableFrom": "videos", "tableTo": "topics", "columnsFrom": ["topic_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.badges": {"name": "badges", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "badge_type": {"name": "badge_type", "type": "badge_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "icon_url": {"name": "icon_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "criteria": {"name": "criteria", "type": "text", "primaryKey": false, "notNull": true}, "xp_reward": {"name": "xp_reward", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"badges_name_unique": {"name": "badges_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_badges": {"name": "user_badges", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "badge_id": {"name": "badge_id", "type": "uuid", "primaryKey": false, "notNull": true}, "earned_at": {"name": "earned_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "progress": {"name": "progress", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_badges_user_id_users_id_fk": {"name": "user_badges_user_id_users_id_fk", "tableFrom": "user_badges", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_badges_badge_id_badges_id_fk": {"name": "user_badges_badge_id_badges_id_fk", "tableFrom": "user_badges", "tableTo": "badges", "columnsFrom": ["badge_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_badges_user_id_badge_id_unique": {"name": "user_badges_user_id_badge_id_unique", "nullsNotDistinct": false, "columns": ["user_id", "badge_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_streaks": {"name": "user_streaks", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "streak_type": {"name": "streak_type", "type": "streak_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "current_streak": {"name": "current_streak", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "longest_streak": {"name": "longest_streak", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_activity_date": {"name": "last_activity_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_streaks_user_id_users_id_fk": {"name": "user_streaks_user_id_users_id_fk", "tableFrom": "user_streaks", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_streaks_user_id_streak_type_unique": {"name": "user_streaks_user_id_streak_type_unique", "nullsNotDistinct": false, "columns": ["user_id", "streak_type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_xp": {"name": "user_xp", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "total_xp": {"name": "total_xp", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "current_level": {"name": "current_level", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "xp_to_next_level": {"name": "xp_to_next_level", "type": "integer", "primaryKey": false, "notNull": true, "default": 100}, "last_xp_earned": {"name": "last_xp_earned", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_xp_source": {"name": "last_xp_source", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "last_xp_earned_at": {"name": "last_xp_earned_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_xp_user_id_users_id_fk": {"name": "user_xp_user_id_users_id_fk", "tableFrom": "user_xp", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_profiles": {"name": "user_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "standard": {"name": "standard", "type": "standard", "typeSchema": "public", "primaryKey": false, "notNull": true}, "school_name": {"name": "school_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "educational_board": {"name": "educational_board", "type": "educational_board", "typeSchema": "public", "primaryKey": false, "notNull": true}, "interests": {"name": "interests", "type": "text", "primaryKey": false, "notNull": false}, "dislikes": {"name": "dislikes", "type": "text", "primaryKey": false, "notNull": false}, "profile_completed": {"name": "profile_completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_profiles_user_id_users_id_fk": {"name": "user_profiles_user_id_users_id_fk", "tableFrom": "user_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_profiles_user_id_unique": {"name": "user_profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_relationships": {"name": "user_relationships", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "primary_user_id": {"name": "primary_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "related_user_id": {"name": "related_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "relationship_type": {"name": "relationship_type", "type": "relationship_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_relationships_primary_user_id_users_id_fk": {"name": "user_relationships_primary_user_id_users_id_fk", "tableFrom": "user_relationships", "tableTo": "users", "columnsFrom": ["primary_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_relationships_related_user_id_users_id_fk": {"name": "user_relationships_related_user_id_users_id_fk", "tableFrom": "user_relationships", "tableTo": "users", "columnsFrom": ["related_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_relationships_created_by_users_id_fk": {"name": "user_relationships_created_by_users_id_fk", "tableFrom": "user_relationships", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_relationships_primary_user_id_related_user_id_relationship_type_unique": {"name": "user_relationships_primary_user_id_related_user_id_relationship_type_unique", "nullsNotDistinct": false, "columns": ["primary_user_id", "related_user_id", "relationship_type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_role_assignments": {"name": "user_role_assignments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "assigned_by": {"name": "assigned_by", "type": "uuid", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_role_assignments_user_id_users_id_fk": {"name": "user_role_assignments_user_id_users_id_fk", "tableFrom": "user_role_assignments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_role_assignments_role_id_user_roles_id_fk": {"name": "user_role_assignments_role_id_user_roles_id_fk", "tableFrom": "user_role_assignments", "tableTo": "user_roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_role_assignments_assigned_by_users_id_fk": {"name": "user_role_assignments_assigned_by_users_id_fk", "tableFrom": "user_role_assignments", "tableTo": "users", "columnsFrom": ["assigned_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_role_assignments_user_id_role_id_unique": {"name": "user_role_assignments_user_id_role_id_unique", "nullsNotDistinct": false, "columns": ["user_id", "role_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_roles": {"name": "user_roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "role_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_roles_name_unique": {"name": "user_roles_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_notes": {"name": "user_notes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "topic_id": {"name": "topic_id", "type": "uuid", "primaryKey": false, "notNull": true}, "video_id": {"name": "video_id", "type": "uuid", "primaryKey": false, "notNull": false}, "note_type": {"name": "note_type", "type": "note_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'GENERAL'"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "video_timestamp": {"name": "video_timestamp", "type": "integer", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_notes_user_id_users_id_fk": {"name": "user_notes_user_id_users_id_fk", "tableFrom": "user_notes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_notes_topic_id_topics_id_fk": {"name": "user_notes_topic_id_topics_id_fk", "tableFrom": "user_notes", "tableTo": "topics", "columnsFrom": ["topic_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_notes_video_id_videos_id_fk": {"name": "user_notes_video_id_videos_id_fk", "tableFrom": "user_notes", "tableTo": "videos", "columnsFrom": ["video_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_quiz_attempts": {"name": "user_quiz_attempts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "quiz_id": {"name": "quiz_id", "type": "uuid", "primaryKey": false, "notNull": true}, "attempt_number": {"name": "attempt_number", "type": "integer", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "max_score": {"name": "max_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "percentage": {"name": "percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "time_spent": {"name": "time_spent", "type": "integer", "primaryKey": false, "notNull": true}, "answers": {"name": "answers", "type": "text", "primaryKey": false, "notNull": true}, "is_passed": {"name": "is_passed", "type": "boolean", "primaryKey": false, "notNull": true}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_quiz_attempts_user_id_users_id_fk": {"name": "user_quiz_attempts_user_id_users_id_fk", "tableFrom": "user_quiz_attempts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_quiz_attempts_quiz_id_quizzes_id_fk": {"name": "user_quiz_attempts_quiz_id_quizzes_id_fk", "tableFrom": "user_quiz_attempts", "tableTo": "quizzes", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_topic_progress": {"name": "user_topic_progress", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "topic_id": {"name": "topic_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "progress_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'NOT_STARTED'"}, "progress_percentage": {"name": "progress_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true, "default": "'0.00'"}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "time_spent": {"name": "time_spent", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_topic_progress_user_id_users_id_fk": {"name": "user_topic_progress_user_id_users_id_fk", "tableFrom": "user_topic_progress", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_topic_progress_topic_id_topics_id_fk": {"name": "user_topic_progress_topic_id_topics_id_fk", "tableFrom": "user_topic_progress", "tableTo": "topics", "columnsFrom": ["topic_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_topic_progress_user_id_topic_id_unique": {"name": "user_topic_progress_user_id_topic_id_unique", "nullsNotDistinct": false, "columns": ["user_id", "topic_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_video_progress": {"name": "user_video_progress", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "video_id": {"name": "video_id", "type": "uuid", "primaryKey": false, "notNull": true}, "watched_duration": {"name": "watched_duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_duration": {"name": "total_duration", "type": "integer", "primaryKey": false, "notNull": true}, "progress_percentage": {"name": "progress_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true, "default": "'0.00'"}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_watched_at": {"name": "last_watched_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "watch_count": {"name": "watch_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_video_progress_user_id_users_id_fk": {"name": "user_video_progress_user_id_users_id_fk", "tableFrom": "user_video_progress", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_video_progress_video_id_videos_id_fk": {"name": "user_video_progress_video_id_videos_id_fk", "tableFrom": "user_video_progress", "tableTo": "videos", "columnsFrom": ["video_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_video_progress_user_id_video_id_unique": {"name": "user_video_progress_user_id_video_id_unique", "nullsNotDistinct": false, "columns": ["user_id", "video_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pets": {"name": "pets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "type": {"name": "type", "type": "pet_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "avatar_url": {"name": "avatar_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "personality_traits": {"name": "personality_traits", "type": "text", "primaryKey": false, "notNull": true}, "help_messages": {"name": "help_messages", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"pets_type_unique": {"name": "pets_type_unique", "nullsNotDistinct": false, "columns": ["type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_pets": {"name": "user_pets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "pet_id": {"name": "pet_id", "type": "uuid", "primaryKey": false, "notNull": true}, "custom_name": {"name": "custom_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "selected_at": {"name": "selected_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "interaction_count": {"name": "interaction_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_interaction_at": {"name": "last_interaction_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "personality_data": {"name": "personality_data", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_pets_user_id_users_id_fk": {"name": "user_pets_user_id_users_id_fk", "tableFrom": "user_pets", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_pets_pet_id_pets_id_fk": {"name": "user_pets_pet_id_pets_id_fk", "tableFrom": "user_pets", "tableTo": "pets", "columnsFrom": ["pet_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_pets_user_id_unique": {"name": "user_pets_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.content_status": {"name": "content_status", "schema": "public", "values": ["DRAFT", "PUBLISHED", "ARCHIVED"]}, "public.difficulty_level": {"name": "difficulty_level", "schema": "public", "values": ["BEGINNER", "INTERMEDIATE", "ADVANCED"]}, "public.question_type": {"name": "question_type", "schema": "public", "values": ["MULTIPLE_CHOICE", "FILL_IN_BLANK", "DRAG_DROP", "TRUE_FALSE"]}, "public.badge_type": {"name": "badge_type", "schema": "public", "values": ["ACHIEVEMENT", "MILESTONE", "SPECIAL_EVENT", "STREAK"]}, "public.streak_type": {"name": "streak_type", "schema": "public", "values": ["DAILY_LOGIN", "VIDEO_COMPLETION", "QUIZ_COMPLETION", "NOTE_CREATION"]}, "public.educational_board": {"name": "educational_board", "schema": "public", "values": ["CBSE", "ICSE", "STATE_BOARD"]}, "public.standard": {"name": "standard", "schema": "public", "values": ["8th", "9th", "10th"]}, "public.relationship_type": {"name": "relationship_type", "schema": "public", "values": ["PARENT_CHILD", "TEACHER_STUDENT"]}, "public.role_type": {"name": "role_type", "schema": "public", "values": ["STUDENT", "PARENT", "TEACHER", "ADMIN"]}, "public.note_type": {"name": "note_type", "schema": "public", "values": ["GENERAL", "VIDEO_TIMESTAMP", "QUIZ_REVIEW"]}, "public.progress_status": {"name": "progress_status", "schema": "public", "values": ["NOT_STARTED", "IN_PROGRESS", "COMPLETED"]}, "public.personality_trait": {"name": "personality_trait", "schema": "public", "values": ["FRIENDLY", "ENERGETIC", "CALM", "PLAYFUL", "WISE", "CURIOUS", "SUPPORTIVE", "ENCOURAGING"]}, "public.pet_type": {"name": "pet_type", "schema": "public", "values": ["CAT", "DOG", "TURTLE", "PANDA", "MOUSE", "RABBIT"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}