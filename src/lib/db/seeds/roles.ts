import { db } from "../index";
import { userRoles } from "../schema/roles";

export async function seedRoles() {
  console.log(db);

  const roles = [
    {
      name: "STUDENT" as const,
      description:
        "Student role with access to learning content and progress tracking",
      permissions: JSON.stringify([
        "read:own_profile",
        "update:own_profile",
        "read:content",
        "create:notes",
        "read:own_progress",
        "update:own_progress",
        "take:quizzes",
        "watch:videos",
      ]),
    },
    {
      name: "PARENT" as const,
      description:
        "Parent role with access to children's progress and communication",
      permissions: JSON.stringify([
        "read:own_profile",
        "update:own_profile",
        "read:children_progress",
        "read:children_profile",
        "communicate:teachers",
      ]),
    },
    {
      name: "TEACH<PERSON>" as const,
      description:
        "Teacher role with access to student management and content creation",
      permissions: JSON.stringify([
        "read:own_profile",
        "update:own_profile",
        "read:student_progress",
        "read:student_profile",
        "create:content",
        "update:content",
        "grade:assignments",
        "communicate:parents",
      ]),
    },
    {
      name: "ADMIN" as const,
      description: "Administrator role with full system access",
      permissions: JSON.stringify([
        "read:all",
        "create:all",
        "update:all",
        "delete:all",
        "manage:users",
        "manage:roles",
        "manage:system",
      ]),
    },
  ];

  for (const role of roles) {
    await db
      .insert(userRoles)
      .values(role)
      .onConflictDoNothing({ target: userRoles.name });
  }
}
