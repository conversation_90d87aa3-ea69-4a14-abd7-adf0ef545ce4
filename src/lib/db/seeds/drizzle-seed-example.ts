/**
 * Example implementation using drizzle-seed
 * This demonstrates how to use drizzle-seed for more advanced seeding scenarios
 *
 * To use this:
 * 1. Ensure DATABASE_URL is set in your environment
 * 2. Run: tsx src/lib/db/seeds/drizzle-seed-example.ts
 */

import { seed } from "drizzle-seed";
import { db } from "../index";
import { subjects } from "../schema/content";
import { pets } from "../schema/pets";
import { userRoles } from "../schema/roles";
import { userProfiles, users } from "../schema/users";

export async function seedWithDrizzleSeed() {
	console.log("🌱 Starting database seeding with drizzle-seed...");

	try {
		// Seed user roles with specific data
		await seed(db, { userRoles }).refine((funcs) => ({
			userRoles: {
				count: 4,
				columns: {
					name: funcs.valuesFromArray({
						values: ["STUDENT", "PARENT", "TEACHER", "ADMIN"],
					}),
					description: funcs.valuesFromArray({
						values: [
							"Student role with access to learning content and progress tracking",
							"Parent role with access to children's progress and communication",
							"Teacher role with access to student management and content creation",
							"Administrator role with full system access",
						],
					}),
					permissions: funcs.valuesFromArray({
						values: [
							JSON.stringify([
								"read:own_profile",
								"update:own_profile",
								"read:content",
								"create:notes",
								"read:own_progress",
								"update:own_progress",
								"take:quizzes",
								"watch:videos",
							]),
							JSON.stringify([
								"read:own_profile",
								"update:own_profile",
								"read:children_progress",
								"read:children_profile",
								"communicate:teachers",
							]),
							JSON.stringify([
								"read:own_profile",
								"update:own_profile",
								"read:student_progress",
								"read:student_profile",
								"create:content",
								"update:content",
								"grade:assignments",
								"communicate:parents",
							]),
							JSON.stringify([
								"read:all",
								"create:all",
								"update:all",
								"delete:all",
								"manage:users",
								"manage:roles",
								"manage:system",
							]),
						],
					}),
				},
			},
		}));

		// Seed pets with specific data
		await seed(db, { pets }).refine((funcs) => ({
			pets: {
				count: 6,
				columns: {
					type: funcs.valuesFromArray({
						values: ["CAT", "DOG", "TURTLE", "PANDA", "MOUSE", "RABBIT"],
					}),
					name: funcs.valuesFromArray({
						values: [
							"Whiskers",
							"Buddy",
							"Sage",
							"Bamboo",
							"Squeaky",
							"Hopscotch",
						],
					}),
					description: funcs.valuesFromArray({
						values: [
							"A curious and playful cat who loves to explore new topics with you.",
							"A loyal and energetic dog who's your best study companion.",
							"A wise and calm turtle who believes in steady progress.",
							"A gentle and supportive panda who makes learning fun and stress-free.",
							"A quick and clever mouse who loves solving problems.",
							"An energetic and optimistic rabbit who loves hopping from topic to topic.",
						],
					}),
					imageUrl: funcs.valuesFromArray({
						values: [
							"/images/pets/cat.png",
							"/images/pets/dog.png",
							"/images/pets/turtle.png",
							"/images/pets/panda.png",
							"/images/pets/mouse.png",
							"/images/pets/rabbit.png",
						],
					}),
					avatarUrl: funcs.valuesFromArray({
						values: [
							"/images/pets/cat-avatar.png",
							"/images/pets/dog-avatar.png",
							"/images/pets/turtle-avatar.png",
							"/images/pets/panda-avatar.png",
							"/images/pets/mouse-avatar.png",
							"/images/pets/rabbit-avatar.png",
						],
					}),
					personalityTraits: funcs.valuesFromArray({
						values: [
							JSON.stringify(["CURIOUS", "PLAYFUL", "ENCOURAGING"]),
							JSON.stringify(["FRIENDLY", "ENERGETIC", "SUPPORTIVE"]),
							JSON.stringify(["WISE", "CALM", "ENCOURAGING"]),
							JSON.stringify(["CALM", "SUPPORTIVE", "FRIENDLY"]),
							JSON.stringify(["CURIOUS", "ENERGETIC", "WISE"]),
							JSON.stringify(["ENERGETIC", "PLAYFUL", "ENCOURAGING"]),
						],
					}),
					helpMessages: funcs.valuesFromArray({
						values: [
							JSON.stringify([
								"Purrfect! You're doing great!",
								"Let's explore this topic together!",
							]),
							JSON.stringify([
								"Woof! You're amazing!",
								"Let's fetch some knowledge!",
							]),
							JSON.stringify([
								"Slow and steady wins the race!",
								"Every step forward is progress!",
							]),
							JSON.stringify([
								"You're doing bamboo-tifully!",
								"Learning should be fun!",
							]),
							JSON.stringify([
								"Let's squeak through this problem!",
								"Small steps lead to big discoveries!",
							]),
							JSON.stringify([
								"Hop to it! You're doing great!",
								"Let's bounce to the next topic!",
							]),
						],
					}),
				},
			},
		}));

		// Generate random users and profiles for testing
		await seed(db, { users, userProfiles }).refine((funcs) => ({
			users: {
				count: 50,
				columns: {
					email: funcs.email(),
					emailVerified: funcs.boolean(),
					passwordHash: funcs.string(),
					isActive: funcs.boolean(),
				},
			},
			userProfiles: {
				count: 50,
				columns: {
					fullName: funcs.fullName(),
					standard: funcs.valuesFromArray({
						values: ["8th", "9th", "10th"],
						isUnique: false,
					}),
					schoolName: funcs.valuesFromArray({
						values: [
							"Delhi Public School",
							"Kendriya Vidyalaya",
							"St. Xavier's School",
							"Modern School",
							"Ryan International School",
							"DAV Public School",
						],
						isUnique: false,
					}),
					educationalBoard: funcs.valuesFromArray({
						values: ["CBSE"],
						isUnique: false,
					}),
					profileCompleted: funcs.boolean(),
				},
			},
		}));

		// Generate subjects with realistic data
		await seed(db, { subjects }).refine((funcs) => ({
			subjects: {
				count: 24, // 8 subjects × 3 standards
				columns: {
					name: funcs.valuesFromArray({
						values: [
							"Mathematics",
							"Science",
							"English",
							"Hindi",
							"Social Studies",
							"Computer Science",
							"Physics",
							"Chemistry",
							"Biology",
						],
						isUnique: false,
					}),
					standard: funcs.valuesFromArray({
						values: ["8th", "9th", "10th"],
						isUnique: false,
					}),
					educationalBoard: funcs.valuesFromArray({
						values: ["CBSE"],
						isUnique: false,
					}),
					iconUrl: funcs.valuesFromArray({
						values: [
							"/images/subjects/math.svg",
							"/images/subjects/science.svg",
							"/images/subjects/english.svg",
							"/images/subjects/hindi.svg",
							"/images/subjects/social-studies.svg",
							"/images/subjects/computer.svg",
							"/images/subjects/physics.svg",
							"/images/subjects/chemistry.svg",
							"/images/subjects/biology.svg",
						],
						isUnique: false,
					}),
					colorCode: funcs.valuesFromArray({
						values: [
							"#3B82F6",
							"#10B981",
							"#8B5CF6",
							"#F59E0B",
							"#EF4444",
							"#6366F1",
							"#06B6D4",
							"#84CC16",
							"#22C55E",
						],
						isUnique: false,
					}),
					sortOrder: funcs.int({ minValue: 1, maxValue: 10 }),
				},
			},
		}));

		console.log(
			"🎉 Database seeding completed successfully with drizzle-seed!",
		);
	} catch (error) {
		console.error("❌ Error seeding database:", error);
		throw error;
	}
}

// Run seeding if this file is executed directly
if (require.main === module) {
	seedWithDrizzleSeed()
		.then(() => {
			console.log("Drizzle-seed seeding finished");
			process.exit(0);
		})
		.catch((error) => {
			console.error("Drizzle-seed seeding failed:", error);
			process.exit(1);
		});
}
