import { seedPets } from "./pets";
import { seedRoles } from "./roles";
import { seedSubjects } from "./subjects";

export async function seedDatabase() {
  console.log("🌱 Starting database seeding...");

  try {
    // Seed in order of dependencies
    await seedRoles();
    console.log("✅ Roles seeded");

    await seedPets();
    console.log("✅ Pets seeded");

    await seedSubjects();
    console.log("✅ Subjects seeded");

    console.log("🎉 Database seeding completed successfully!");
  } catch (error) {
    console.error("❌ Error seeding database:", error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log("Seeding finished");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Seeding failed:", error);
      process.exit(1);
    });
}
