import { db } from "../index";
import { pets } from "../schema/pets";

export async function seedPets() {
	const petData = [
		{
			type: "CAT" as const,
			name: "Whiskers",
			description: "A curious and playful cat who loves to explore new topics with you. Whiskers is always ready to pounce on learning opportunities!",
			imageUrl: "/images/pets/cat.png",
			avatarUrl: "/images/pets/cat-avatar.png",
			personalityTraits: JSON.stringify(["CURIOUS", "PLAYFUL", "ENCOURAGING"]),
			helpMessages: JSON.stringify([
				"Purrfect! You're doing great!",
				"Let's explore this topic together!",
				"I'm curious about this too - let's learn!",
				"Don't worry, we'll figure this out step by step!",
				"Time for a learning adventure!",
			]),
		},
		{
			type: "DOG" as const,
			name: "<PERSON>",
			description: "A loyal and energetic dog who's your best study companion. Buddy is always excited to learn new things and celebrate your achievements!",
			imageUrl: "/images/pets/dog.png",
			avatarUrl: "/images/pets/dog-avatar.png",
			personalityTraits: JSON.stringify(["FRIENDLY", "ENERGETIC", "SUPPORTIVE"]),
			helpMessages: JSON.stringify([
				"Woof! You're amazing!",
				"Let's fetch some knowledge!",
				"I believe in you - you can do this!",
				"Great job! I'm so proud of you!",
				"Ready for the next challenge? Let's go!",
			]),
		},
		{
			type: "TURTLE" as const,
			name: "Sage",
			description: "A wise and calm turtle who believes in steady progress. Sage reminds you that learning is a journey, not a race.",
			imageUrl: "/images/pets/turtle.png",
			avatarUrl: "/images/pets/turtle-avatar.png",
			personalityTraits: JSON.stringify(["WISE", "CALM", "ENCOURAGING"]),
			helpMessages: JSON.stringify([
				"Slow and steady wins the race!",
				"Take your time - understanding is more important than speed.",
				"Every step forward is progress!",
				"Wisdom comes with patience and practice.",
				"You're building strong foundations for learning!",
			]),
		},
		{
			type: "PANDA" as const,
			name: "Bamboo",
			description: "A gentle and supportive panda who makes learning fun and stress-free. Bamboo is always there to cheer you up and keep you motivated!",
			imageUrl: "/images/pets/panda.png",
			avatarUrl: "/images/pets/panda-avatar.png",
			personalityTraits: JSON.stringify(["CALM", "SUPPORTIVE", "FRIENDLY"]),
			helpMessages: JSON.stringify([
				"You're doing bamboo-tifully!",
				"Learning should be fun - let's enjoy this!",
				"Take a deep breath, you've got this!",
				"I'm here to support you every step of the way!",
				"Remember, mistakes are just learning opportunities!",
			]),
		},
		{
			type: "MOUSE" as const,
			name: "Squeaky",
			description: "A quick and clever mouse who loves solving problems. Squeaky helps you think outside the box and find creative solutions!",
			imageUrl: "/images/pets/mouse.png",
			avatarUrl: "/images/pets/mouse-avatar.png",
			personalityTraits: JSON.stringify(["CURIOUS", "ENERGETIC", "WISE"]),
			helpMessages: JSON.stringify([
				"Let's squeak through this problem together!",
				"I have a clever idea - want to try it?",
				"Small steps lead to big discoveries!",
				"Think like a mouse - find the hidden path!",
				"You're sharper than cheese! Keep going!",
			]),
		},
		{
			type: "RABBIT" as const,
			name: "Hopscotch",
			description: "An energetic and optimistic rabbit who loves hopping from topic to topic. Hopscotch keeps your learning journey exciting and dynamic!",
			imageUrl: "/images/pets/rabbit.png",
			avatarUrl: "/images/pets/rabbit-avatar.png",
			personalityTraits: JSON.stringify(["ENERGETIC", "PLAYFUL", "ENCOURAGING"]),
			helpMessages: JSON.stringify([
				"Hop to it! You're doing great!",
				"Let's bounce to the next topic!",
				"Some-bunny believes in you!",
				"Quick thinking! You're on the right track!",
				"Ready to hop into something new?",
			]),
		},
	];

	for (const pet of petData) {
		await db
			.insert(pets)
			.values(pet)
			.onConflictDoNothing({ target: pets.type });
	}
}
