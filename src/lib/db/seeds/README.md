# Database Seeding

This directory contains database seeding utilities for the LearnFunda application.

## Available Seeding Methods

### 1. Manual Seeding (Recommended for Production)

**Files:**
- `index.ts` - Main seeding orchestrator
- `roles.ts` - User roles and permissions
- `pets.ts` - AI assistant pets
- `subjects.ts` - Educational subjects by standard/board

**Usage:**
```bash
pnpm db:seed
```

**Features:**
- Precise control over data
- Handles conflicts gracefully with `onConflictDoNothing`
- Deterministic and predictable
- Perfect for production environments

### 2. Drizzle-Seed (Advanced/Development)

**File:** `drizzle-seed-example.ts`

**Usage:**
```bash
pnpm db:seed:drizzle
```

**Features:**
- Generates realistic fake data
- Great for development and testing
- Can generate large datasets quickly
- Uses Dr<PERSON><PERSON>'s built-in seeding capabilities

## Environment Setup

Before running any seeding commands, ensure you have:

1. **Database URL configured:**
   ```bash
   DATABASE_URL="postgresql://username:password@localhost:5432/learnfunda"
   ```

2. **Environment variables set:**
   Copy `.env.example` to `.env.local` and fill in the values.

## Seeding Order

The seeding process follows dependency order:

1. **User Roles** - Foundation for role-based access control
2. **Pets** - AI assistant options
3. **Subjects** - Educational content structure
4. **Users & Profiles** (drizzle-seed only) - Test user accounts
5. **Content** (future) - Topics, videos, quizzes

## Data Structure

### User Roles
- **STUDENT**: Learning content access, progress tracking
- **PARENT**: Children's progress monitoring
- **TEACHER**: Student management, content creation
- **ADMIN**: Full system access

### Pets (AI Assistants)
- **Cat (Whiskers)**: Curious and playful
- **Dog (Buddy)**: Loyal and energetic
- **Turtle (Sage)**: Wise and calm
- **Panda (Bamboo)**: Gentle and supportive
- **Mouse (Squeaky)**: Quick and clever
- **Rabbit (Hopscotch)**: Energetic and optimistic

### Subjects
Covers standards 8th, 9th, and 10th for CBSE board:
- Mathematics
- Science (8th) / Physics, Chemistry, Biology (9th, 10th)
- English
- Hindi
- Social Studies
- Computer Science

## Development Tips

1. **Reset database before seeding:**
   ```bash
   pnpm db:drop
   pnpm db:migrate
   pnpm db:seed
   ```

2. **Use drizzle-seed for testing:**
   ```bash
   pnpm db:seed:drizzle
   ```

3. **Check seeded data:**
   Use your database client or build a simple admin interface to verify the seeded data.

## Extending Seeding

To add new seed data:

1. Create a new seed file (e.g., `topics.ts`)
2. Export a seed function
3. Add it to the main `index.ts` orchestrator
4. Update the dependency order as needed

Example:
```typescript
// topics.ts
export async function seedTopics() {
  const topicData = [
    // your topic data
  ];
  
  for (const topic of topicData) {
    await db.insert(topics).values(topic).onConflictDoNothing();
  }
}
```
