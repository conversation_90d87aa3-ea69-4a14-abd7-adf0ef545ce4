import {
	pgTable,
	uuid,
	varchar,
	text,
	timestamp,
	boolean,
	integer,
	pgEnum,
	decimal,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { educationalBoardEnum, standardEnum } from "./users";

// Enums
export const difficultyLevelEnum = pgEnum("difficulty_level", [
	"BEGINNER",
	"INTERMEDIATE",
	"ADVANCED",
]);

export const questionTypeEnum = pgEnum("question_type", [
	"MULTIPLE_CHOICE",
	"FILL_IN_BLANK",
	"DRAG_DROP",
	"TRUE_FALSE",
]);

export const contentStatusEnum = pgEnum("content_status", [
	"DRAFT",
	"PUBLISHED",
	"ARCHIVED",
]);

// Subjects table - Academic subjects by standard/board
export const subjects = pgTable("subjects", {
	id: uuid("id").primaryKey().defaultRandom(),
	name: varchar("name", { length: 255 }).notNull(),
	description: text("description"),
	standard: standardEnum("standard").notNull(),
	educationalBoard: educationalBoardEnum("educational_board").notNull(),
	iconUrl: varchar("icon_url", { length: 500 }),
	colorCode: varchar("color_code", { length: 7 }), // Hex color code
	isActive: boolean("is_active").default(true).notNull(),
	sortOrder: integer("sort_order").default(0).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Topics table - Learning topics within subjects
export const topics = pgTable("topics", {
	id: uuid("id").primaryKey().defaultRandom(),
	subjectId: uuid("subject_id")
		.references(() => subjects.id, { onDelete: "cascade" })
		.notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	description: text("description"),
	difficultyLevel: difficultyLevelEnum("difficulty_level").notNull(),
	estimatedDuration: integer("estimated_duration"), // in minutes
	prerequisites: text("prerequisites"), // JSON array of prerequisite topic IDs
	learningObjectives: text("learning_objectives"), // JSON array
	isActive: boolean("is_active").default(true).notNull(),
	sortOrder: integer("sort_order").default(0).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Videos table - Learning content videos
export const videos = pgTable("videos", {
	id: uuid("id").primaryKey().defaultRandom(),
	topicId: uuid("topic_id")
		.references(() => topics.id, { onDelete: "cascade" })
		.notNull(),
	title: varchar("title", { length: 255 }).notNull(),
	description: text("description"),
	videoUrl: varchar("video_url", { length: 500 }).notNull(),
	thumbnailUrl: varchar("thumbnail_url", { length: 500 }),
	duration: integer("duration").notNull(), // in seconds
	transcriptUrl: varchar("transcript_url", { length: 500 }),
	status: contentStatusEnum("status").default("DRAFT").notNull(),
	isActive: boolean("is_active").default(true).notNull(),
	sortOrder: integer("sort_order").default(0).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Quizzes table - Assessment content
export const quizzes = pgTable("quizzes", {
	id: uuid("id").primaryKey().defaultRandom(),
	topicId: uuid("topic_id")
		.references(() => topics.id, { onDelete: "cascade" })
		.notNull(),
	title: varchar("title", { length: 255 }).notNull(),
	description: text("description"),
	instructions: text("instructions"),
	timeLimit: integer("time_limit"), // in minutes, null for no limit
	passingScore: decimal("passing_score", { precision: 5, scale: 2 }), // percentage
	maxAttempts: integer("max_attempts"), // null for unlimited
	isActive: boolean("is_active").default(true).notNull(),
	sortOrder: integer("sort_order").default(0).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Quiz questions table
export const quizQuestions = pgTable("quiz_questions", {
	id: uuid("id").primaryKey().defaultRandom(),
	quizId: uuid("quiz_id")
		.references(() => quizzes.id, { onDelete: "cascade" })
		.notNull(),
	questionType: questionTypeEnum("question_type").notNull(),
	questionText: text("question_text").notNull(),
	options: text("options"), // JSON array for multiple choice options
	correctAnswer: text("correct_answer").notNull(),
	explanation: text("explanation"),
	points: integer("points").default(1).notNull(),
	sortOrder: integer("sort_order").default(0).notNull(),
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Zod schemas for validation
export const insertSubjectSchema = createInsertSchema(subjects, {
	name: z.string().min(1).max(255),
	colorCode: z.string().regex(/^#[0-9A-F]{6}$/i).optional(),
});

export const selectSubjectSchema = createSelectSchema(subjects);

export const insertTopicSchema = createInsertSchema(topics, {
	name: z.string().min(1).max(255),
	estimatedDuration: z.number().positive().optional(),
});

export const selectTopicSchema = createSelectSchema(topics);

export const insertVideoSchema = createInsertSchema(videos, {
	title: z.string().min(1).max(255),
	videoUrl: z.string().url(),
	duration: z.number().positive(),
});

export const selectVideoSchema = createSelectSchema(videos);

export const insertQuizSchema = createInsertSchema(quizzes, {
	title: z.string().min(1).max(255),
	timeLimit: z.number().positive().optional(),
	maxAttempts: z.number().positive().optional(),
});

export const selectQuizSchema = createSelectSchema(quizzes);

export const insertQuizQuestionSchema = createInsertSchema(quizQuestions, {
	questionText: z.string().min(1),
	points: z.number().positive(),
});

export const selectQuizQuestionSchema = createSelectSchema(quizQuestions);

// Types
export type Subject = typeof subjects.$inferSelect;
export type NewSubject = typeof subjects.$inferInsert;
export type Topic = typeof topics.$inferSelect;
export type NewTopic = typeof topics.$inferInsert;
export type Video = typeof videos.$inferSelect;
export type NewVideo = typeof videos.$inferInsert;
export type Quiz = typeof quizzes.$inferSelect;
export type NewQuiz = typeof quizzes.$inferInsert;
export type QuizQuestion = typeof quizQuestions.$inferSelect;
export type NewQuizQuestion = typeof quizQuestions.$inferInsert;
