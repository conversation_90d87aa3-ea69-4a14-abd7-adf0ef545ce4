import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";

// Enums
export const educationalBoardEnum = pgEnum("educational_board", [
  "CBSE",
  "ICSE",
  "STATE_BOARD",
]);

export const standardEnum = pgEnum("standard", ["8th", "9th", "10th"]);

// Users table - Core authentication data
export const users = pgTable("users", {
  id: uuid("id").primaryKey().defaultRandom(),
  email: varchar("email", { length: 255 }).notNull().unique(),
  emailVerified: boolean("email_verified").default(false).notNull(),
  passwordHash: text("password_hash").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  lastLoginAt: timestamp("last_login_at"),
  isActive: boolean("is_active").default(true).notNull(),
});

// User profiles table - Extended user information
export const userProfiles = pgTable("user_profiles", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .references(() => users.id, { onDelete: "cascade" })
    .notNull()
    .unique(),
  fullName: varchar("full_name", { length: 255 }).notNull(),
  standard: standardEnum("standard").notNull(),
  schoolName: varchar("school_name", { length: 255 }).notNull(),
  educationalBoard: educationalBoardEnum("educational_board").notNull(),
  interests: text("interests"), // JSON array of interests
  dislikes: text("dislikes"), // JSON array of dislikes
  profileCompleted: boolean("profile_completed").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Zod schemas for validation
export const insertUserSchema = createInsertSchema(users, {
  email: z.string().email(),
  passwordHash: z.string().min(1),
});

export const selectUserSchema = createSelectSchema(users);

export const insertUserProfileSchema = createInsertSchema(userProfiles, {
  fullName: z.string().min(1).max(255),
  schoolName: z.string().min(1).max(255),
  interests: z.string().optional(),
  dislikes: z.string().optional(),
});

export const selectUserProfileSchema = createSelectSchema(userProfiles);

// Types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type UserProfile = typeof userProfiles.$inferSelect;
export type NewUserProfile = typeof userProfiles.$inferInsert;
