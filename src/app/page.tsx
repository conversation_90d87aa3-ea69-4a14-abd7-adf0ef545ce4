"use client";

import { useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";

export default function ComingSoon() {
	const [email, setEmail] = useState("");
	const [isSubmitted, setIsSubmitted] = useState(false);
	const [isLoading, setIsLoading] = useState(false);

	const handleSubmit = async (e?: React.FormEvent) => {
		e?.preventDefault();
		setIsLoading(true);

		try {
			// Send email to Resend API
			const response = await fetch("/api/subscribe", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ email }),
			});

			if (response.ok) {
				setIsSubmitted(true);
				setEmail("");
			} else {
				throw new Error("Failed to subscribe");
			}
		} catch (error) {
			console.error("Error subscribing:", error);
			// You could add error state handling here
			toast.error("Failed to subscribe. Please try again.");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 to-white flex flex-col">
			{/* Header */}
			<header className="p-6">
				<div className="flex items-center">
					<div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
						<span className="text-white font-bold text-sm">L</span>
					</div>
					<span className="ml-3 text-xl font-semibold text-gray-800">
						LearnFunda
					</span>
				</div>
			</header>

			{/* Main Content */}
			<main className="flex-1 flex items-center justify-center px-6">
				<div className="max-w-2xl mx-auto text-center">
					{/* Illustration */}
					<div className="mb-12 relative">
						<div className="relative mx-auto w-64 h-64">
							{/* Background clouds */}
							<div className="absolute top-8 left-4 w-16 h-10 bg-blue-100 rounded-full opacity-60"></div>
							<div className="absolute top-6 right-8 w-20 h-12 bg-blue-100 rounded-full opacity-40"></div>
							<div className="absolute bottom-12 left-12 w-18 h-11 bg-blue-100 rounded-full opacity-50"></div>
							<div className="absolute bottom-8 right-4 w-16 h-10 bg-blue-100 rounded-full opacity-60"></div>

							{/* Main figure */}
							<div className="absolute bottom-0 left-1/2 transform -translate-x-1/2">
								{/* Head */}
								<div className="relative w-24 h-24 bg-white rounded-full border-4 border-blue-200 mx-auto mb-2">
									{/* Brain */}
									<div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-16 h-16 bg-blue-600 rounded-full">
										{/* Brain details */}
										<div className="absolute inset-2 border-2 border-blue-300 rounded-full opacity-60"></div>
										<div className="absolute top-3 left-3 w-2 h-2 bg-blue-300 rounded-full"></div>
										<div className="absolute top-5 right-4 w-1.5 h-1.5 bg-blue-300 rounded-full"></div>
										<div className="absolute bottom-4 left-4 w-1.5 h-1.5 bg-blue-300 rounded-full"></div>
										<div className="absolute bottom-3 right-3 w-2 h-2 bg-blue-300 rounded-full"></div>
									</div>

									{/* Plus signs around head */}
									<div className="absolute -top-2 left-1/2 transform -translate-x-1/2 text-blue-600 text-lg font-bold">
										+
									</div>
									<div className="absolute top-4 -left-3 text-blue-600 text-sm font-bold">
										+
									</div>
									<div className="absolute top-4 -right-3 text-blue-600 text-sm font-bold">
										+
									</div>
								</div>

								{/* Body */}
								<div className="w-20 h-16 bg-white rounded-t-full border-4 border-blue-200 mx-auto">
									<div className="mt-4 w-3 h-8 bg-blue-600 rounded-full mx-auto"></div>
								</div>

								{/* Base/Platform */}
								<div className="w-32 h-8 bg-blue-100 rounded-full mx-auto -mt-2 opacity-80"></div>
							</div>
						</div>
					</div>

					{/* Heading */}
					<h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
						We're working on something exciting!
					</h1>

					{/* Description */}
					<p className="text-lg text-gray-600 mb-12 max-w-md mx-auto leading-relaxed">
						Get ready for a powerful addition to LearnFunda. Designed to enhance
						your learning experience.
					</p>

					{/* Email Form */}
					<div className="max-w-md mx-auto">
						{!isSubmitted ? (
							<div className="flex flex-col sm:flex-row gap-3">
								<input
									type="email"
									value={email}
									onChange={(e) => setEmail(e.target.value)}
									placeholder="Enter your email address"
									className="flex-1 px-4 py-3 border-2 border-blue-200 rounded-lg focus:outline-none focus:border-blue-500 transition-colors"
								/>
								<Button
									onClick={() => handleSubmit()}
									disabled={isLoading || !email.includes("@")}
									className="px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 h-auto"
								>
									{isLoading ? "Joining..." : "Notify Me"}
								</Button>
							</div>
						) : (
							<div className="bg-green-50 border-2 border-green-200 rounded-lg p-6">
								<div className="text-green-600 text-2xl mb-2">✓</div>
								<h3 className="text-lg font-semibold text-green-800 mb-1">
									Thanks for joining!
								</h3>
								<p className="text-green-700">
									We'll notify you when we launch.
								</p>
							</div>
						)}
					</div>
				</div>
			</main>

			{/* Footer */}
			<footer className="p-6 border-t border-blue-100">
				<div className="text-center">
					<p className="text-gray-600 mb-4">
						Follow us on social media for updates
					</p>
					<div className="flex justify-center space-x-6">
						<a
							href="/"
							className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center hover:bg-blue-200 transition-colors"
						>
							<svg
								className="w-5 h-5 text-blue-600"
								fill="currentColor"
								viewBox="0 0 24 24"
							>
								<title>Instagram</title>
								<path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
							</svg>
						</a>
						<a
							href="/"
							className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center hover:bg-blue-200 transition-colors"
						>
							<svg
								className="w-5 h-5 text-blue-600"
								fill="currentColor"
								viewBox="0 0 24 24"
							>
								<title>Facebook</title>
								<path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z" />
							</svg>
						</a>
						<a
							href="/"
							className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center hover:bg-blue-200 transition-colors"
						>
							<svg
								className="w-5 h-5 text-blue-600"
								fill="currentColor"
								viewBox="0 0 24 24"
							>
								<title>LinkedIn</title>
								<path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.222.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.017 0z.017" />
							</svg>
						</a>
						<a
							href="/"
							className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center hover:bg-blue-200 transition-colors"
						>
							<svg
								className="w-5 h-5 text-blue-600"
								fill="currentColor"
								viewBox="0 0 24 24"
							>
								<title>X</title>
								<path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
							</svg>
						</a>
					</div>
				</div>
			</footer>
		</div>
	);
}
